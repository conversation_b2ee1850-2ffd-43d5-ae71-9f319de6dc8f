import 'package:flutter_cache_manager/flutter_cache_manager.dart';

/// Custom cache manager for optimizing image loading performance
/// Similar to <PERSON><PERSON><PERSON><PERSON>'s image caching strategy
class CustomCacheManager {
  static const key = 'mr_garments_cache';

  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 7), // Keep images for 7 days
      maxNrOfCacheObjects: 200, // Maximum number of cached objects
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: HttpFileService(),
    ),
  );

  /// Cache manager specifically for chat images
  static const chatKey = 'mr_garments_chat_cache';

  static CacheManager chatInstance = CacheManager(
    Config(
      chatKey,
      stalePeriod: const Duration(days: 30), // Keep chat images longer
      maxNrOfCacheObjects: 500, // More cache objects for chat
      repo: JsonCacheInfoRepository(databaseName: chatKey),
      fileService: HttpFileService(),
    ),
  );

  /// Cache manager for catalog and product images
  static const catalogKey = 'mr_garments_catalog_cache';

  static CacheManager catalogInstance = CacheManager(
    Config(
      catalogKey,
      stalePeriod: const Duration(days: 14), // Keep catalog images for 2 weeks
      maxNrOfCacheObjects: 300,
      repo: JsonCacheInfoRepository(databaseName: catalogKey),
      fileService: HttpFileService(),
    ),
  );

  /// Clear all caches
  static Future<void> clearAllCaches() async {
    await instance.emptyCache();
    await chatInstance.emptyCache();
    await catalogInstance.emptyCache();
  }

  /// Clear only chat cache
  static Future<void> clearChatCache() async {
    await chatInstance.emptyCache();
  }

  /// Clear only catalog cache
  static Future<void> clearCatalogCache() async {
    await catalogInstance.emptyCache();
  }

  /// Get cache size information (simplified)
  static Future<Map<String, String>> getCacheInfo() async {
    return {
      'general': 'Cache enabled',
      'chat': 'Cache enabled',
      'catalog': 'Cache enabled',
    };
  }
}
